const axios = require('axios');

async function testFortuneFeatures() {
  console.log('🔮 测试算命功能权限...\n');

  const baseURL = 'http://localhost:3001/api';
  let authToken = null;

  try {
    // 1. 登录获取token
    console.log('1. 用户登录...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.success) {
      authToken = loginResponse.data.data.token;
      console.log('✅ 登录成功');
    } else {
      throw new Error('登录失败');
    }

    const headers = { 'Authorization': `Bearer ${authToken}` };

    // 2. 测试八字精算
    console.log('\n2. 测试八字精算 (BaZi)...');
    try {
      const baziResponse = await axios.post(`${baseURL}/fortune/bazi`, {
        birthYear: 1990,
        birthMonth: 5,
        birthDay: 15,
        birthHour: 14,
        gender: 'male',
        birthPlace: '北京市'
      }, { headers });

      if (baziResponse.data.success) {
        console.log('✅ 八字精算访问成功');
        console.log(`   结果长度: ${baziResponse.data.result?.length || 0} 字符`);
      } else {
        console.log('❌ 八字精算失败:', baziResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 八字精算权限被拒绝:', error.response?.data?.message || error.message);
    }

    // 3. 测试每日运势
    console.log('\n3. 测试每日运势 (Daily Fortune)...');
    try {
      const dailyResponse = await axios.post(`${baseURL}/fortune/daily`, {
        birthYear: 1990,
        birthMonth: 5,
        birthDay: 15,
        birthHour: 14,
        gender: 'male'
      }, { headers });

      if (dailyResponse.data.success) {
        console.log('✅ 每日运势访问成功');
        console.log(`   结果长度: ${dailyResponse.data.result?.length || 0} 字符`);
      } else {
        console.log('❌ 每日运势失败:', dailyResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 每日运势权限被拒绝:', error.response?.data?.message || error.message);
    }

    // 4. 测试塔罗占卜
    console.log('\n4. 测试塔罗占卜 (Tarot Reading)...');
    try {
      const tarotResponse = await axios.post(`${baseURL}/fortune/tarot`, {
        question: '我的事业发展如何？',
        birthYear: 1990,
        birthMonth: 5,
        birthDay: 15,
        birthHour: 14,
        gender: 'male'
      }, { headers });

      if (tarotResponse.data.success) {
        console.log('✅ 塔罗占卜访问成功');
        console.log(`   结果长度: ${tarotResponse.data.result?.length || 0} 字符`);
      } else {
        console.log('❌ 塔罗占卜失败:', tarotResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 塔罗占卜权限被拒绝:', error.response?.data?.message || error.message);
    }

    // 5. 测试幸运物品
    console.log('\n5. 测试幸运物品 (Lucky Items)...');
    try {
      const luckyResponse = await axios.post(`${baseURL}/fortune/lucky-items`, {
        birthYear: 1990,
        birthMonth: 5,
        birthDay: 15,
        birthHour: 14,
        gender: 'male'
      }, { headers });

      if (luckyResponse.data.success) {
        console.log('✅ 幸运物品访问成功');
        console.log(`   结果长度: ${luckyResponse.data.result?.length || 0} 字符`);
      } else {
        console.log('❌ 幸运物品失败:', luckyResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 幸运物品权限被拒绝:', error.response?.data?.message || error.message);
    }

    // 6. 检查会员状态变化
    console.log('\n6. 检查会员状态变化...');
    const finalMembershipResponse = await axios.get(`${baseURL}/membership/status`, { headers });
    
    if (finalMembershipResponse.data.success) {
      const membership = finalMembershipResponse.data.data;
      console.log('✅ 最终会员状态:');
      console.log(`   计划: ${membership.plan_id}`);
      console.log(`   状态: ${membership.is_active ? '激活' : '未激活'}`);
      console.log(`   剩余积分: ${membership.remaining_credits}`);
      console.log(`   过期时间: ${membership.expires_at}`);
    }

    console.log('\n🎉 算命功能权限测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误详情:', error.response.data);
    }
  }
}

testFortuneFeatures();
