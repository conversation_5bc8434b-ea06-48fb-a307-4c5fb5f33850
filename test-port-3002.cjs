const axios = require('axios');

async function testPort3002() {
  console.log('🧪 测试端口3002上的后端服务...\n');

  const baseURL = 'http://localhost:3002';
  
  try {
    // 1. 测试健康检查
    console.log('🏥 测试健康检查...');
    const healthResponse = await axios.get(`${baseURL}/api/health`);
    
    if (healthResponse.status === 200) {
      console.log('✅ 健康检查成功');
      console.log('📊 响应数据:', healthResponse.data);
    } else {
      console.log('❌ 健康检查失败');
    }

    // 2. 测试用户登录
    console.log('\n🔐 测试用户登录...');
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, loginData);
    
    if (loginResponse.data.success) {
      console.log('✅ 登录成功');
      const user = loginResponse.data.data.user;
      const token = loginResponse.data.data.token;
      
      console.log('👤 用户信息:');
      console.log(`   ID: ${user.id}`);
      console.log(`   姓名: ${user.name}`);
      console.log(`   邮箱: ${user.email}`);
      console.log(`   性别: ${user.gender}`);
      
      // 3. 测试获取用户资料
      console.log('\n📋 测试获取用户资料...');
      const profileResponse = await axios.get(`${baseURL}/api/user/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (profileResponse.data.success) {
        console.log('✅ 用户资料获取成功');
        const profile = profileResponse.data.data;
        console.log('📝 完整资料:');
        console.log(`   姓名: ${profile.name}`);
        console.log(`   性别: ${profile.gender}`);
        console.log(`   出生: ${profile.birth_year}-${profile.birth_month}-${profile.birth_day} ${profile.birth_hour}时`);
        console.log(`   出生地: ${profile.birth_place || '未设置'}`);
        console.log(`   时区: ${profile.timezone || '未设置'}`);
        console.log(`   邮箱验证: ${profile.is_email_verified ? '已验证' : '未验证'}`);
        
        console.log('\n🎉 所有测试通过！后端服务运行正常！');
        console.log('🌐 您现在可以访问前端页面了：http://localhost:5173');
        
      } else {
        console.log('❌ 用户资料获取失败:', profileResponse.data.message);
      }
      
    } else {
      console.log('❌ 登录失败:', loginResponse.data.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.response?.data?.message || error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 建议：');
      console.log('1. 检查后端服务是否在端口3002上运行');
      console.log('2. 运行: cd backend && node server.js');
    }
  }
}

// 运行测试
testPort3002();
