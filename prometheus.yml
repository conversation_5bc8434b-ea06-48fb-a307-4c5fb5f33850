global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  # Monitor the Node.js application
  - job_name: "destiny-app"
    static_configs:
      - targets: ["app:3000"]
    metrics_path: "/api/metrics"
    scrape_interval: 30s

  # Monitor Nginx
  - job_name: "nginx"
    static_configs:
      - targets: ["nginx:80"]
    metrics_path: "/nginx_status"
    scrape_interval: 30s

  # Monitor PostgreSQL
  - job_name: "postgres"
    static_configs:
      - targets: ["postgres:5432"]
    scrape_interval: 30s

  # Monitor Redis
  - job_name: "redis"
    static_configs:
      - targets: ["redis:6379"]
    scrape_interval: 30s
