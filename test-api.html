<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>API Test Page</h1>
    
    <div>
        <h2>Login Test</h2>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="newpassword123">
        <button onclick="testLogin()">Login</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div>
        <h2>Profile Test</h2>
        <button onclick="testProfile()">Get Profile</button>
        <div id="profileResult" class="result"></div>
    </div>

    <script>
        let authToken = '';

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                document.getElementById('loginResult').innerHTML = `
                    <h3>Login Response:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                if (data.token) {
                    authToken = data.token;
                    console.log('Token saved:', authToken);
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = `
                    <h3>Login Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testProfile() {
            if (!authToken) {
                document.getElementById('profileResult').innerHTML = 'Please login first to get token';
                return;
            }
            
            try {
                const response = await fetch('http://localhost:3001/api/user/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                document.getElementById('profileResult').innerHTML = `
                    <h3>Profile Response:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('profileResult').innerHTML = `
                    <h3>Profile Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
