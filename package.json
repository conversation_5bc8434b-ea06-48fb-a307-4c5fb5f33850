{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"antd": "^5.26.6", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "helmet": "^8.1.0", "i18next": "^25.3.2", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "nodemailer": "^7.0.5", "rate-limiter-flexible": "^7.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.3", "remark-gfm": "^4.0.1", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}