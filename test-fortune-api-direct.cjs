const axios = require('axios');

async function testFortuneAPIDirect() {
  console.log('🔮 直接测试算命API...\n');

  const baseURL = 'http://localhost:3001/api';
  let authToken = null;

  try {
    // 1. 登录获取token
    console.log('1. 用户登录...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.success) {
      authToken = loginResponse.data.data.token;
      console.log('✅ 登录成功');
    } else {
      throw new Error('登录失败');
    }

    const headers = { 
      'Authorization': `Bearer ${authToken}`,
      'Accept-Language': 'zh',
      'Content-Type': 'application/json'
    };

    // 2. 测试八字精算API
    console.log('\n2. 测试八字精算API...');
    try {
      const baziResponse = await axios.post(`${baseURL}/fortune/bazi`, {}, { headers });
      
      if (baziResponse.data.success) {
        console.log('✅ 八字精算API调用成功');
        console.log(`   分析类型: ${baziResponse.data.data?.type || '未知'}`);
        console.log(`   分析结果长度: ${baziResponse.data.data?.analysis?.length || 0} 字符`);
        console.log(`   时间戳: ${baziResponse.data.data?.timestamp || '未知'}`);
      } else {
        console.log('❌ 八字精算API调用失败:', baziResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 八字精算API错误:', error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.log('   完整错误信息:', JSON.stringify(error.response.data, null, 2));
      }
    }

    // 3. 测试每日运势API
    console.log('\n3. 测试每日运势API...');
    try {
      const dailyResponse = await axios.post(`${baseURL}/fortune/daily`, {}, { headers });
      
      if (dailyResponse.data.success) {
        console.log('✅ 每日运势API调用成功');
        console.log(`   分析类型: ${dailyResponse.data.data?.type || '未知'}`);
        console.log(`   分析结果长度: ${dailyResponse.data.data?.analysis?.length || 0} 字符`);
      } else {
        console.log('❌ 每日运势API调用失败:', dailyResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 每日运势API错误:', error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.log('   完整错误信息:', JSON.stringify(error.response.data, null, 2));
      }
    }

    // 4. 测试塔罗占卜API
    console.log('\n4. 测试塔罗占卜API...');
    try {
      const tarotResponse = await axios.post(`${baseURL}/fortune/tarot`, {
        question: '我的事业发展如何？'
      }, { headers });
      
      if (tarotResponse.data.success) {
        console.log('✅ 塔罗占卜API调用成功');
        console.log(`   分析类型: ${tarotResponse.data.data?.type || '未知'}`);
        console.log(`   问题: ${tarotResponse.data.data?.question || '未知'}`);
        console.log(`   分析结果长度: ${tarotResponse.data.data?.analysis?.length || 0} 字符`);
      } else {
        console.log('❌ 塔罗占卜API调用失败:', tarotResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 塔罗占卜API错误:', error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.log('   完整错误信息:', JSON.stringify(error.response.data, null, 2));
      }
    }

    console.log('\n🎉 算命API直接测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误详情:', error.response.data);
    }
  }
}

testFortuneAPIDirect();
