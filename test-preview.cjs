const axios = require('axios');

async function testPreview() {
  console.log('🌐 测试预览服务 (端口4173)...\n');

  try {
    const response = await axios.get('http://localhost:4173', {
      timeout: 10000,
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    console.log('✅ 预览服务正常运行');
    console.log('📊 状态码:', response.status);
    console.log('📝 内容类型:', response.headers['content-type']);
    console.log('📏 响应大小:', response.data.length, '字符');
    
    // 检查是否包含React应用的标识
    if (response.data.includes('root')) {
      console.log('✅ React应用结构正常');
    }
    
    if (response.data.includes('Celestial Fortune-Telling')) {
      console.log('✅ 应用标题正确');
    }
    
    console.log('\n🎉 预览测试完成！');
    console.log('💡 现在可以在浏览器中访问: http://localhost:4173');
    console.log('💡 这是生产构建版本，性能更好');
    
  } catch (error) {
    console.error('❌ 预览测试失败:', error.message);
  }
}

testPreview();
