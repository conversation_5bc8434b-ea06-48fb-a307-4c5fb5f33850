const axios = require('axios');

async function testBackend() {
  console.log('🧪 测试后端服务 (端口3002)...\n');

  const baseURL = 'http://localhost:3002';
  
  try {
    // 1. 测试健康检查
    console.log('🏥 测试健康检查...');
    const healthResponse = await axios.get(`${baseURL}/api/health`);
    
    if (healthResponse.status === 200) {
      console.log('✅ 健康检查成功');
      console.log('📊 服务状态:', healthResponse.data.status);
    }

    // 2. 测试用户登录
    console.log('\n🔐 测试用户登录...');
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, loginData);
    
    if (loginResponse.data.success) {
      console.log('✅ 登录成功');
      const user = loginResponse.data.data.user;
      const token = loginResponse.data.data.token;
      
      console.log('👤 用户信息:');
      console.log(`   ID: ${user.id}`);
      console.log(`   姓名: ${user.name}`);
      console.log(`   邮箱: ${user.email}`);
      console.log(`   性别: ${user.gender}`);
      console.log(`   出生: ${user.birth_year}-${user.birth_month}-${user.birth_day} ${user.birth_hour}时`);
      
      // 3. 测试获取用户资料
      console.log('\n📋 测试获取用户资料...');
      const profileResponse = await axios.get(`${baseURL}/api/user/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (profileResponse.data.success) {
        console.log('✅ 用户资料获取成功');
        const profile = profileResponse.data.data;
        console.log('📝 完整资料:');
        console.log(`   姓名: ${profile.name}`);
        console.log(`   性别: ${profile.gender}`);
        console.log(`   出生: ${profile.birth_year}-${profile.birth_month}-${profile.birth_day} ${profile.birth_hour}时`);
        console.log(`   出生地: ${profile.birth_place || '未设置'}`);
        console.log(`   时区: ${profile.timezone || '未设置'}`);
        console.log(`   邮箱验证: ${profile.is_email_verified ? '已验证' : '未验证'}`);
        
        console.log('\n🎉 后端服务运行正常！');
        console.log('💡 问题可能在前端连接，请检查：');
        console.log('   1. 前端是否正在运行 (http://localhost:5173)');
        console.log('   2. 前端API配置是否指向端口3002');
        console.log('   3. 浏览器是否有缓存问题');
        
      } else {
        console.log('❌ 用户资料获取失败:', profileResponse.data.message);
      }
      
    } else {
      console.log('❌ 登录失败:', loginResponse.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data?.message || error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 后端服务未运行，请启动：');
      console.log('   cd backend && node server.js');
    }
  }
}

testBackend();
