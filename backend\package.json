{"name": "destiny-backend", "version": "1.0.0", "description": "Backend API for Destiny Fortune Telling App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/initDatabase.js", "test-email": "node scripts/testEmail.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "nodemailer": "^6.9.7", "joi": "^17.11.0", "rate-limiter-flexible": "^2.4.2", "express-rate-limit": "^6.10.0", "axios": "^1.6.0", "dotenv": "^16.3.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["fortune-telling", "api", "nodejs", "express"], "author": "Destiny Team", "license": "MIT"}