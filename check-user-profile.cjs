const axios = require('axios');

async function checkUserProfile() {
  console.log('👤 检查用户个人资料...\n');

  const baseURL = 'http://localhost:3001/api';
  let authToken = null;

  try {
    // 1. 登录获取token
    console.log('1. 用户登录...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.success) {
      authToken = loginResponse.data.data.token;
      console.log('✅ 登录成功');
    } else {
      throw new Error('登录失败');
    }

    const headers = { 'Authorization': `Bearer ${authToken}` };

    // 2. 获取用户资料
    console.log('\n2. 获取用户资料...');
    const profileResponse = await axios.get(`${baseURL}/user/profile`, { headers });
    
    if (profileResponse.data.success) {
      const user = profileResponse.data.user;
      console.log('✅ 用户资料获取成功:');
      console.log(`   用户ID: ${user.id}`);
      console.log(`   姓名: ${user.name || '未设置'}`);
      console.log(`   邮箱: ${user.email}`);
      console.log(`   性别: ${user.gender || '未设置'}`);
      console.log(`   出生年份: ${user.birthYear || '未设置'}`);
      console.log(`   出生月份: ${user.birthMonth || '未设置'}`);
      console.log(`   出生日期: ${user.birthDay || '未设置'}`);
      console.log(`   出生时辰: ${user.birthHour || '未设置'}`);
      console.log(`   出生地点: ${user.birthPlace || '未设置'}`);
      console.log(`   时区: ${user.timezone || '未设置'}`);
      console.log(`   邮箱验证: ${user.isEmailVerified ? '已验证' : '未验证'}`);

      // 检查资料完整性
      const missingFields = [];
      if (!user.birthYear) missingFields.push('出生年份');
      if (!user.birthMonth) missingFields.push('出生月份');
      if (!user.birthDay) missingFields.push('出生日期');
      if (!user.gender) missingFields.push('性别');
      if (!user.birthPlace) missingFields.push('出生地点');

      if (missingFields.length > 0) {
        console.log('\n❌ 资料不完整，缺少以下信息:');
        missingFields.forEach(field => console.log(`   - ${field}`));
        console.log('\n💡 这就是为什么算命功能显示"需要完善资料"的原因！');
      } else {
        console.log('\n✅ 用户资料完整，可以进行算命分析');
      }

      if (user.membership) {
        console.log('\n💎 会员信息:');
        console.log(`   计划: ${user.membership.plan_id}`);
        console.log(`   状态: ${user.membership.is_active ? '激活' : '未激活'}`);
        console.log(`   剩余积分: ${user.membership.remaining_credits}`);
      }
    }

    console.log('\n🎉 用户资料检查完成！');

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误详情:', error.response.data);
    }
  }
}

checkUserProfile();
