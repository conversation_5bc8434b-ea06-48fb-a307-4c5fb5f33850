const axios = require('axios');

async function testServe() {
  console.log('🌐 测试静态服务器 (端口8080)...\n');

  try {
    const response = await axios.get('http://localhost:8080', {
      timeout: 10000,
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    console.log('✅ 静态服务器正常运行');
    console.log('📊 状态码:', response.status);
    console.log('📝 内容类型:', response.headers['content-type']);
    console.log('📏 响应大小:', response.data.length, '字符');
    
    // 检查是否包含React应用的标识
    if (response.data.includes('root')) {
      console.log('✅ React应用结构正常');
    }
    
    if (response.data.includes('Celestial Fortune-Telling')) {
      console.log('✅ 应用标题正确');
    }
    
    // 检查是否包含构建后的资源
    if (response.data.includes('/assets/')) {
      console.log('✅ 构建资源正常加载');
    }
    
    console.log('\n🎉 静态服务器测试完成！');
    console.log('💡 现在可以在浏览器中访问: http://localhost:8080');
    console.log('💡 这是生产构建版本，性能最佳');
    
    // 测试后端连接
    console.log('\n🔧 测试后端连接...');
    const backendResponse = await axios.get('http://localhost:3001/api/health');
    
    if (backendResponse.status === 200) {
      console.log('✅ 后端服务正常运行');
      console.log('📊 后端状态:', backendResponse.data.status);
    }
    
    console.log('\n🎯 完整系统状态:');
    console.log('✅ 前端: http://localhost:8080 (生产版本)');
    console.log('✅ 后端: http://localhost:3001/api (开发版本)');
    console.log('✅ 测试账号: <EMAIL> / password123');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testServe();
