console.log('🔄 Starting frontend debug...');

// 检查Node.js环境
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);
console.log('Current directory:', process.cwd());

// 检查关键文件是否存在
const fs = require('fs');
const path = require('path');

const criticalFiles = [
  'package.json',
  'vite.config.ts',
  'index.html',
  'src/main.tsx',
  'src/App.tsx'
];

console.log('\n📁 检查关键文件...');
criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} 存在`);
  } else {
    console.log(`❌ ${file} 不存在`);
  }
});

// 检查node_modules
console.log('\n📦 检查依赖...');
if (fs.existsSync('node_modules')) {
  console.log('✅ node_modules 存在');
  
  // 检查关键依赖
  const keyDeps = ['react', 'vite', '@vitejs/plugin-react'];
  keyDeps.forEach(dep => {
    if (fs.existsSync(`node_modules/${dep}`)) {
      console.log(`✅ ${dep} 已安装`);
    } else {
      console.log(`❌ ${dep} 未安装`);
    }
  });
} else {
  console.log('❌ node_modules 不存在');
}

// 检查端口占用
console.log('\n🔌 检查端口状态...');
const { exec } = require('child_process');

exec('netstat -an | findstr ":5173"', (error, stdout, stderr) => {
  if (stdout) {
    console.log('⚠️ 端口5173可能被占用:');
    console.log(stdout);
  } else {
    console.log('✅ 端口5173空闲');
  }
});

// 尝试启动Vite
console.log('\n🚀 尝试启动Vite...');

const { spawn } = require('child_process');

const vite = spawn('npx', ['vite', '--port', '5174', '--host'], {
  stdio: ['inherit', 'pipe', 'pipe'],
  shell: true
});

vite.stdout.on('data', (data) => {
  console.log('📤 Vite输出:', data.toString());
});

vite.stderr.on('data', (data) => {
  console.error('❌ Vite错误:', data.toString());
});

vite.on('close', (code) => {
  console.log(`🔚 Vite进程退出，代码: ${code}`);
});

vite.on('error', (error) => {
  console.error('❌ Vite启动失败:', error);
});

// 设置超时检查
setTimeout(() => {
  console.log('\n⏰ 30秒后检查状态...');
  
  // 检查进程是否还在运行
  if (!vite.killed) {
    console.log('✅ Vite进程仍在运行');
    
    // 尝试访问服务
    const axios = require('axios');
    axios.get('http://localhost:5174')
      .then(response => {
        console.log('✅ 前端服务响应正常');
        console.log('📊 状态码:', response.status);
        console.log('🌐 可以访问: http://localhost:5174');
      })
      .catch(error => {
        console.log('❌ 前端服务访问失败:', error.message);
      });
  } else {
    console.log('❌ Vite进程已退出');
  }
}, 30000);

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，关闭Vite...');
  vite.kill();
  process.exit(0);
});
