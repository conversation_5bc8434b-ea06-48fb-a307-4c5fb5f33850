const axios = require('axios');

async function testFrontend() {
  console.log('🌐 测试前端服务...\n');

  try {
    const response = await axios.get('http://localhost:5173', {
      timeout: 10000,
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    console.log('✅ 前端服务正常运行');
    console.log('📊 状态码:', response.status);
    console.log('📝 内容类型:', response.headers['content-type']);
    console.log('📏 响应大小:', response.data.length, '字符');
    
    // 检查是否包含React应用的标识
    if (response.data.includes('root')) {
      console.log('✅ React应用结构正常');
    }
    
    if (response.data.includes('Celestial Fortune-Telling')) {
      console.log('✅ 应用标题正确');
    }
    
    console.log('\n🎉 前端测试完成！');
    console.log('💡 现在可以在浏览器中访问: http://localhost:5173');
    
  } catch (error) {
    console.error('❌ 前端测试失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 解决方案:');
      console.log('1. 确保前端服务运行: npm run dev');
      console.log('2. 检查端口5173是否被占用');
      console.log('3. 尝试重启Vite服务器');
    }
  }
}

testFrontend();
