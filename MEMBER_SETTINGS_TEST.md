# 🔮 Member Settings Testing Guide

## ✅ 修复完成！

页面错误已经修复，现在可以正常访问会员设置功能了。

## 🧪 测试步骤

### 1. **访问主页**
- 打开 http://localhost:5173/
- 确认页面正常加载，没有错误

### 2. **登录测试账号**
- 使用测试账号登录：
  - **邮箱**: `<EMAIL>`
  - **密码**: `123456`
- 或者注册新账号进行测试

### 3. **访问会员设置**
- 登录成功后，查看右上角导航
- **桌面端**: 点击设置图标 (⚙️)
- **移动端**: 点击菜单按钮，选择 "Member Settings"

### 4. **测试个人资料设置**
- 切换到 "Profile Settings" 标签
- 查看警告提示：**"You can only update your profile once"**
- 填写个人信息：
  - 姓名
  - 性别
  - 出生日期和时间
  - 出生地点
- 点击 "Update Profile (One Time Only)" 按钮

### 5. **测试一次性限制**
- 更新资料后，再次尝试修改
- 应该看到：**"Profile Already Updated"** 按钮被禁用
- 表单字段应该变为只读状态

### 6. **测试密码修改**
- 切换到 "Change Password" 标签
- 填写：
  - 当前密码
  - 新密码 (至少6位)
  - 确认新密码
- 测试密码可见性切换按钮 (👁️)
- 点击 "Change Password" 按钮

### 7. **测试导航功能**
- 点击 "← Back to Main" 返回主页
- 测试登出功能

## 🔧 技术验证

### 后端API测试：
```bash
# 获取用户资料
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3001/api/user/profile

# 更新资料 (只能一次)
curl -X PUT -H "Authorization: Bearer YOUR_TOKEN" -H "Content-Type: application/json" \
  -d '{"name":"Test User","gender":"male"}' \
  http://localhost:3001/api/user/profile

# 修改密码
curl -X PUT -H "Authorization: Bearer YOUR_TOKEN" -H "Content-Type: application/json" \
  -d '{"currentPassword":"123456","newPassword":"newpass123"}' \
  http://localhost:3001/api/user/change-password
```

### 数据库验证：
```sql
-- 检查用户表结构
.schema users

-- 查看用户资料更新次数
SELECT email, profile_updated_count FROM users;
```

## 🎯 预期结果

### ✅ 成功场景：
1. **首次资料更新** → 成功，`profile_updated_count` 增加到 1
2. **密码修改** → 成功，密码哈希更新
3. **界面响应** → 实时反馈，状态更新
4. **导航功能** → 流畅切换页面

### ❌ 限制场景：
1. **二次资料更新** → 被阻止，显示警告
2. **错误密码** → 验证失败，显示错误
3. **弱密码** → 客户端验证阻止
4. **未登录访问** → 重定向或错误提示

## 🐛 常见问题排查

### 如果页面无法加载：
1. 检查前端服务器：`npm run dev`
2. 检查后端服务器：`npm start` (在backend目录)
3. 查看浏览器控制台错误

### 如果API调用失败：
1. 检查JWT token是否有效
2. 验证后端路由是否正确
3. 查看网络请求状态

### 如果数据库操作失败：
1. 检查SQLite数据库文件
2. 验证表结构是否正确
3. 查看后端日志

## 📱 移动端测试

### 响应式设计验证：
- 在不同屏幕尺寸下测试
- 确认触摸友好的界面
- 验证移动端导航菜单

## 🔒 安全性测试

### 验证安全措施：
1. **JWT验证** → 未登录无法访问
2. **密码加密** → bcrypt哈希存储
3. **输入验证** → 前后端双重验证
4. **CSRF保护** → 安全的API调用

## 🎉 测试完成标志

当以下所有功能正常工作时，测试完成：

- ✅ 页面正常加载，无错误
- ✅ 登录后显示导航菜单
- ✅ 会员设置页面可访问
- ✅ 个人资料一次性更新限制生效
- ✅ 密码修改功能正常
- ✅ 所有表单验证工作
- ✅ 导航和返回功能正常
- ✅ 移动端响应式设计正常

---

**🔮 现在你的算命应用已经具备了完整的会员设置功能！**

用户可以安全地管理他们的账户，同时确保算命服务的准确性和完整性。
