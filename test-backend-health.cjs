const axios = require('axios');

async function testBackendHealth() {
  console.log('🔍 测试后端健康状态...\n');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查 API...');
    const healthResponse = await axios.get('http://localhost:3001/api/health');
    console.log('✅ 后端健康检查成功');
    console.log('   状态:', healthResponse.data.status);
    console.log('   消息:', healthResponse.data.message);
    console.log('   时间:', healthResponse.data.timestamp);

    // 测试登录
    console.log('\n2. 测试登录 API...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.success) {
      console.log('✅ 登录成功');
      const token = loginResponse.data.data.token;
      console.log('   Token:', token.substring(0, 30) + '...');

      // 测试用户资料
      console.log('\n3. 测试用户资料 API...');
      const profileResponse = await axios.get('http://localhost:3001/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📋 完整响应数据:', JSON.stringify(profileResponse.data, null, 2));

      if (profileResponse.data.success) {
        console.log('✅ 用户资料获取成功');
        const user = profileResponse.data.user;

        if (user) {
          console.log('   用户ID:', user.id);
          console.log('   姓名:', user.name);
          console.log('   邮箱:', user.email);
          console.log('   性别:', user.gender);
          console.log('   出生:', `${user.birthYear}-${user.birthMonth}-${user.birthDay} ${user.birthHour}时`);
          console.log('   出生地:', user.birthPlace || '未设置');
          console.log('   邮箱验证:', user.isEmailVerified ? '已验证' : '未验证');
          console.log('   资料更新次数:', user.profileUpdatedCount);

          if (user.membership) {
            console.log('   会员状态:', user.membership.is_active ? '激活' : '未激活');
            console.log('   会员计划:', user.membership.plan_id);
          }
        } else {
          console.log('❌ 用户数据为空');
        }
      } else {
        console.log('❌ 用户资料获取失败:', profileResponse.data.message);
      }
    } else {
      console.log('❌ 登录失败:', loginResponse.data.message);
    }

    console.log('\n🎉 后端测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误详情:', error.response.data);
    }
    if (error.code === 'ECONNREFUSED') {
      console.error('   💡 提示: 后端服务器可能没有运行在 localhost:3001');
    }
  }
}

testBackendHealth();
