const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function checkMembership() {
  console.log('🔍 检查测试用户会员状态...\n');

  const dbPath = path.join(__dirname, 'backend', 'database', 'destiny.db');
  const db = new sqlite3.Database(dbPath);

  try {
    // 查询测试用户信息
    const user = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id, email, name FROM users WHERE email = ?',
        ['<EMAIL>'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!user) {
      console.log('❌ 测试用户不存在');
      return;
    }

    console.log('👤 用户信息:');
    console.log(`   ID: ${user.id}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   姓名: ${user.name}`);

    // 查询会员信息
    const memberships = await new Promise((resolve, reject) => {
      db.all(
        'SELECT * FROM memberships WHERE user_id = ? ORDER BY created_at DESC',
        [user.id],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    console.log('\n💎 会员记录:');
    if (memberships.length === 0) {
      console.log('   无会员记录');
    } else {
      memberships.forEach((membership, index) => {
        console.log(`   记录 ${index + 1}:`);
        console.log(`     计划: ${membership.plan_id}`);
        console.log(`     状态: ${membership.is_active ? '激活' : '未激活'}`);
        console.log(`     剩余积分: ${membership.remaining_credits}`);
        console.log(`     过期时间: ${membership.expires_at}`);
        console.log(`     创建时间: ${membership.created_at}`);
        console.log(`     更新时间: ${membership.updated_at}`);
        console.log('');
      });
    }

    // 查询会员计划
    const plans = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM membership_plans', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('📋 可用会员计划:');
    plans.forEach(plan => {
      console.log(`   ${plan.id}: ${plan.name} - ${plan.price}元`);
      console.log(`     描述: ${plan.description}`);
      console.log(`     积分: ${plan.credits}`);
      console.log(`     有效期: ${plan.duration_days}天`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  } finally {
    db.close();
  }
}

checkMembership();
