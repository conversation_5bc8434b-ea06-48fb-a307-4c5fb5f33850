<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Token</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Debug Token and API</h1>
    
    <div class="section">
        <h2>1. Check LocalStorage Token</h2>
        <button onclick="checkToken()">Check Token</button>
        <div id="tokenResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>2. <PERSON><PERSON> and Get New Token</h2>
        <button onclick="login()">Login</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>3. Test User Profile API</h2>
        <button onclick="testProfile()">Test Profile API</button>
        <div id="profileResult" class="result"></div>
    </div>
    
    <div class="section">
        <h2>4. Test Health API</h2>
        <button onclick="testHealth()">Test Health API</button>
        <div id="healthResult" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3001/api';
        
        function checkToken() {
            const token = localStorage.getItem('authToken');
            const result = document.getElementById('tokenResult');
            
            if (token) {
                result.innerHTML = `<div class="success">Token found: ${token.substring(0, 50)}...</div>`;
                
                // Try to decode JWT payload
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    result.innerHTML += `<div>User ID: ${payload.userId}</div>`;
                    result.innerHTML += `<div>Email: ${payload.email}</div>`;
                    result.innerHTML += `<div>Expires: ${new Date(payload.exp * 1000).toLocaleString()}</div>`;
                    
                    if (payload.exp * 1000 < Date.now()) {
                        result.innerHTML += `<div class="error">Token is expired!</div>`;
                    } else {
                        result.innerHTML += `<div class="success">Token is valid</div>`;
                    }
                } catch (e) {
                    result.innerHTML += `<div class="error">Cannot decode token: ${e.message}</div>`;
                }
            } else {
                result.innerHTML = `<div class="error">No token found in localStorage</div>`;
            }
        }
        
        async function login() {
            const result = document.getElementById('loginResult');
            result.innerHTML = 'Logging in...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    localStorage.setItem('authToken', data.data.token);
                    result.innerHTML = `<div class="success">Login successful!</div>`;
                    result.innerHTML += `<div>Token: ${data.data.token.substring(0, 50)}...</div>`;
                    result.innerHTML += `<div>User: ${data.data.user.name} (${data.data.user.email})</div>`;
                } else {
                    result.innerHTML = `<div class="error">Login failed: ${data.message}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">Login error: ${error.message}</div>`;
            }
        }
        
        async function testProfile() {
            const result = document.getElementById('profileResult');
            result.innerHTML = 'Testing profile API...';
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                result.innerHTML = `<div class="error">No token found. Please login first.</div>`;
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/user/profile`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        'X-Language': 'zh-CN'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `<div class="success">Profile API successful!</div>`;
                    result.innerHTML += `<div>User: ${data.user.name} (${data.user.email})</div>`;
                    result.innerHTML += `<div>Birth: ${data.user.birthYear}-${data.user.birthMonth}-${data.user.birthDay}</div>`;
                    if (data.user.membership) {
                        result.innerHTML += `<div>Membership: ${data.user.membership.plan_id} (Active: ${data.user.membership.is_active})</div>`;
                    }
                } else {
                    result.innerHTML = `<div class="error">Profile API failed: ${data.message}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">Profile API error: ${error.message}</div>`;
            }
        }
        
        async function testHealth() {
            const result = document.getElementById('healthResult');
            result.innerHTML = 'Testing health API...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                result.innerHTML = `<div class="success">Health API successful!</div>`;
                result.innerHTML += `<div>Status: ${data.status}</div>`;
                result.innerHTML += `<div>Message: ${data.message}</div>`;
                result.innerHTML += `<div>Timestamp: ${data.timestamp}</div>`;
            } catch (error) {
                result.innerHTML = `<div class="error">Health API error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
