import React from 'react';
import { X, Star, Calendar, Sparkles, Gift, Copy, Download } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface FortuneResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  result: any;
  serviceType: string;
}

const FortuneResultModal: React.FC<FortuneResultModalProps> = ({
  isOpen,
  onClose,
  result,
  serviceType
}) => {
  const { t } = useLanguage();
  const [copySuccess, setCopySuccess] = React.useState(false);

  if (!isOpen || !result) return null;

  // 获取服务图标和标题
  const getServiceInfo = (type: string) => {
    switch (type) {
      case 'bazi':
        return {
          icon: <Star className="w-6 h-6" />,
          title: '八字精算',
          titleEn: 'BaZi Analysis',
          color: 'from-purple-500 to-indigo-600'
        };
      case 'dailyfortune':
        return {
          icon: <Calendar className="w-6 h-6" />,
          title: '每日运势',
          titleEn: 'Daily Fortune',
          color: 'from-blue-500 to-cyan-600'
        };
      case 'tarot':
        return {
          icon: <Sparkles className="w-6 h-6" />,
          title: '塔罗占卜',
          titleEn: 'Tarot Reading',
          color: 'from-pink-500 to-rose-600'
        };
      case 'luckyitems':
        return {
          icon: <Gift className="w-6 h-6" />,
          title: '幸运物品',
          titleEn: 'Lucky Items',
          color: 'from-green-500 to-emerald-600'
        };
      default:
        return {
          icon: <Star className="w-6 h-6" />,
          title: '命理分析',
          titleEn: 'Fortune Analysis',
          color: 'from-gray-500 to-gray-600'
        };
    }
  };

  const serviceInfo = getServiceInfo(serviceType);

  // 格式化分析结果文本
  const formatAnalysisText = (text: string) => {
    if (!text) return '';

    // 清理文本：移除乱码、多余空格和换行符
    let cleaned = text
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
      .replace(/[^\u4e00-\u9fa5\u3400-\u4db5\uf900-\ufaff\u3040-\u309f\u30a0-\u30ff\uff00-\uffef\u0020-\u007E\u00A0-\u00FF\u2000-\u206F\u2070-\u209F\u20A0-\u20CF\u2100-\u214F\u2150-\u218F\u2190-\u21FF\u2200-\u22FF\u2300-\u23FF\u2400-\u243F\u2440-\u245F\u2460-\u24FF\u2500-\u257F\u2580-\u259F\u25A0-\u25FF\u2600-\u26FF\u2700-\u27BF\u2800-\u28FF\u2900-\u297F\u2980-\u29FF\u2A00-\u2AFF\u2B00-\u2BFF\u2C00-\u2C5F\u2C60-\u2C7F\u2C80-\u2CFF\u2D00-\u2D2F\u2D30-\u2D7F\u2D80-\u2DDF\u2DE0-\u2DFF\u2E00-\u2E7F\u2E80-\u2EFF\u2F00-\u2FDF\u2FF0-\u2FFF\u3000-\u303F\u3040-\u309F\u30A0-\u30FF\u3100-\u312F\u3130-\u318F\u3190-\u319F\u31A0-\u31BF\u31C0-\u31EF\u31F0-\u31FF\u3200-\u32FF\u3300-\u33FF\u3400-\u4DBF\u4DC0-\u4DFF\u4E00-\u9FBF\uA000-\uA48F\uA490-\uA4CF]/g, '') // 保留中文、英文、数字、标点符号
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/\n{3,}/g, '\n\n') // 合并多个换行符
      .trim();

    // 按段落分割（以双换行符或特定标记分割）
    const paragraphs = cleaned
      .split(/\n\n|\n(?=[一二三四五六七八九十]+[、．])|(?<=。)\s*(?=[一二三四五六七八九十]+[、．])/)
      .filter(p => p.trim().length > 0);

    return paragraphs.map((paragraph, index) => {
      const trimmedParagraph = paragraph.trim();

      // 判断是否为标题
      const isTitle =
        trimmedParagraph.match(/^[一二三四五六七八九十]+[、．]/) || // 数字标题
        trimmedParagraph.match(/^【.*】/) || // 括号标题
        trimmedParagraph.includes('：') && trimmedParagraph.length < 30 || // 短标题
        trimmedParagraph.match(/^(总结|建议|分析|结论|概述|要点)/) || // 关键词标题
        (trimmedParagraph.length < 20 && !trimmedParagraph.includes('。')); // 短句无句号

      // 判断是否为重要信息
      const isImportant =
        trimmedParagraph.includes('重要') ||
        trimmedParagraph.includes('注意') ||
        trimmedParagraph.includes('建议') ||
        trimmedParagraph.includes('提醒');

      return (
        <div
          key={index}
          className={`mb-6 ${
            isTitle
              ? 'font-bold text-xl text-indigo-800 border-l-4 border-indigo-500 pl-4 bg-indigo-50 py-2 rounded-r-lg'
              : isImportant
              ? 'font-medium text-gray-800 bg-yellow-50 border-l-4 border-yellow-400 pl-4 py-2 rounded-r-lg'
              : 'text-gray-700 leading-relaxed text-base'
          }`}
        >
          {trimmedParagraph.split('\n').map((line, lineIndex) => (
            <div key={lineIndex} className={lineIndex > 0 ? 'mt-3' : ''}>
              {line.trim()}
            </div>
          ))}
        </div>
      );
    });
  };

  // 复制结果到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(result.data?.analysis || result.message || '');
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  // 下载结果为文本文件
  const downloadResult = () => {
    const content = result.data?.analysis || result.message || '';
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${serviceInfo.title}_${new Date().toLocaleDateString()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* 头部 */}
        <div className={`bg-gradient-to-r ${serviceInfo.color} text-white p-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                {serviceInfo.icon}
              </div>
              <div>
                <h2 className="text-2xl font-bold">{serviceInfo.title}</h2>
                <p className="text-white text-opacity-90">
                  {new Date().toLocaleString('zh-CN')}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-8 overflow-y-auto max-h-[calc(90vh-200px)] bg-gray-50">
          {/* 问题显示（如果有） */}
          {result.data?.question && (
            <div className="mb-8 p-6 bg-blue-50 border-l-4 border-blue-400 rounded-r-lg shadow-sm">
              <h3 className="font-bold text-blue-800 mb-3 text-lg">💭 咨询问题</h3>
              <p className="text-blue-700 text-base leading-relaxed">{result.data.question}</p>
            </div>
          )}

          {/* 分析结果 */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="font-bold text-gray-800 mb-6 text-lg flex items-center">
              <span className="mr-2">🔮</span>
              分析结果
            </h3>
            <div className="space-y-4">
              {formatAnalysisText(result.data?.analysis || result.message || '')}
            </div>
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="border-t border-gray-200 p-6 bg-white">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="text-sm text-gray-500 flex items-center">
              <span className="mr-2">⏰</span>
              分析完成：{result.data?.timestamp ? new Date(result.data.timestamp).toLocaleString('zh-CN') : new Date().toLocaleString('zh-CN')}
            </div>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={copyToClipboard}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md ${
                  copySuccess
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-600 text-white hover:bg-gray-700'
                }`}
              >
                <Copy className="w-4 h-4" />
                <span>{copySuccess ? '已复制!' : '复制结果'}</span>
              </button>
              <button
                onClick={downloadResult}
                className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <Download className="w-4 h-4" />
                <span>下载文件</span>
              </button>
              <button
                onClick={onClose}
                className="px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FortuneResultModal;
