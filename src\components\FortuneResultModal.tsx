import React from 'react';
import { X, <PERSON>, Calendar, Sparkles, Gift, Copy, Download } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface FortuneResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  result: any;
  serviceType: string;
}

const FortuneResultModal: React.FC<FortuneResultModalProps> = ({
  isOpen,
  onClose,
  result,
  serviceType
}) => {
  const { t } = useLanguage();
  const [copySuccess, setCopySuccess] = React.useState(false);

  if (!isOpen || !result) return null;

  // 获取服务图标和标题
  const getServiceInfo = (type: string) => {
    const { language } = useLanguage();

    switch (type) {
      case 'bazi':
        return {
          icon: <Star className="w-6 h-6" />,
          title: language === 'zh' ? '八字精算' : 'BaZi Analysis',
          color: 'from-purple-500 to-indigo-600'
        };
      case 'dailyfortune':
        return {
          icon: <Calendar className="w-6 h-6" />,
          title: language === 'zh' ? '每日运势' : 'Daily Fortune',
          color: 'from-blue-500 to-cyan-600'
        };
      case 'tarot':
        return {
          icon: <Sparkles className="w-6 h-6" />,
          title: language === 'zh' ? '塔罗占卜' : 'Tarot Reading',
          color: 'from-pink-500 to-rose-600'
        };
      case 'luckyitems':
        return {
          icon: <Gift className="w-6 h-6" />,
          title: language === 'zh' ? '幸运物品' : 'Lucky Items',
          color: 'from-green-500 to-emerald-600'
        };
      default:
        return {
          icon: <Star className="w-6 h-6" />,
          title: language === 'zh' ? '命理分析' : 'Fortune Analysis',
          color: 'from-gray-500 to-gray-600'
        };
    }
  };

  const serviceInfo = getServiceInfo(serviceType);

  // 格式化分析结果文本
  const formatAnalysisText = (text: string) => {
    if (!text) return '';

    // 第一步：清理特殊字符和乱码
    let cleaned = text
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
      .replace(/[#*]+/g, '') // 移除markdown符号
      .replace(/\*\*/g, '') // 移除加粗符号
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim();

    // 第二步：智能分段处理
    let formatted = cleaned
      // 在特定标记前添加换行
      .replace(/(Analysis|Report|Chart|Pillar|Elements|Distribution|Configuration|Personality|Strengths|Areas|Gods|Wealth|Officer|Food|Friend|Rob)/g, '\n\n$1')
      // 在中文标题前添加换行
      .replace(/(一、|二、|三、|四、|五、|六、|七、|八、|九、|十、)/g, '\n\n$1')
      .replace(/(【.*?】)/g, '\n\n$1')
      // 在句号后适当换行
      .replace(/([。！？])\s*([A-Z一二三四五六七八九十])/g, '$1\n\n$2')
      // 处理特殊分隔符
      .replace(/###/g, '\n\n')
      .replace(/---/g, '\n\n')
      // 清理多余换行
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    // 第三步：按段落分割
    const paragraphs = formatted
      .split('\n\n')
      .filter(p => p.trim().length > 0)
      .map(p => p.trim());

    return paragraphs.map((paragraph, index) => {
      // 判断段落类型
      const isMainTitle =
        paragraph.match(/^(Detailed|BaZi|Analysis|Report|Chart)/i) ||
        paragraph.match(/^[一二三四五六七八九十]+[、．]/) ||
        paragraph.match(/^【.*】/);

      const isSubTitle =
        paragraph.match(/^(Four Pillars|Five Elements|Ten Gods|Personality|Core|Strengths|Areas)/i) ||
        paragraph.includes('：') && paragraph.length < 50 ||
        paragraph.match(/^(年柱|月柱|日柱|时柱|五行|十神|性格|优势|建议)/);

      const isImportant =
        paragraph.includes('Strong') ||
        paragraph.includes('Weak') ||
        paragraph.includes('重要') ||
        paragraph.includes('注意') ||
        paragraph.includes('建议');

      const isListItem =
        paragraph.match(/^[✓✗☑☐]/) ||
        paragraph.match(/^\d+\./) ||
        paragraph.match(/^[•·]/);

      // 渲染不同类型的段落
      if (isMainTitle) {
        return (
          <div key={index} className="mb-6 p-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg">
            <h2 className="text-xl font-bold">{paragraph}</h2>
          </div>
        );
      }

      if (isSubTitle) {
        return (
          <div key={index} className="mb-4 p-3 bg-indigo-50 border-l-4 border-indigo-500 rounded-r-lg">
            <h3 className="text-lg font-semibold text-indigo-800">{paragraph}</h3>
          </div>
        );
      }

      if (isImportant) {
        return (
          <div key={index} className="mb-4 p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded-r-lg">
            <p className="text-gray-800 font-medium leading-relaxed">{paragraph}</p>
          </div>
        );
      }

      if (isListItem) {
        return (
          <div key={index} className="mb-3 pl-4">
            <p className="text-gray-700 leading-relaxed">{paragraph}</p>
          </div>
        );
      }

      // 普通段落
      return (
        <div key={index} className="mb-4">
          <p className="text-gray-700 leading-relaxed text-base">{paragraph}</p>
        </div>
      );
    });
  };

  // 复制结果到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(result.data?.analysis || result.message || '');
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  // 下载结果为文本文件
  const downloadResult = () => {
    const content = result.data?.analysis || result.message || '';
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${serviceInfo.title}_${new Date().toLocaleDateString()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* 头部 */}
        <div className={`bg-gradient-to-r ${serviceInfo.color} text-white p-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                {serviceInfo.icon}
              </div>
              <div>
                <h2 className="text-2xl font-bold">{serviceInfo.title}</h2>
                <p className="text-white text-opacity-90">
                  {new Date().toLocaleString('zh-CN')}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-8 overflow-y-auto max-h-[calc(90vh-200px)] bg-gray-50">
          {/* 问题显示（如果有） */}
          {result.data?.question && (
            <div className="mb-8 p-6 bg-blue-50 border-l-4 border-blue-400 rounded-r-lg shadow-sm">
              <h3 className="font-bold text-blue-800 mb-3 text-lg">💭 {t('consultationQuestion') || '咨询问题'}</h3>
              <p className="text-blue-700 text-base leading-relaxed">{result.data.question}</p>
            </div>
          )}

          {/* 分析结果 */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="font-bold text-gray-800 mb-6 text-lg flex items-center">
              <span className="mr-2">🔮</span>
              {t('analysisResult') || '分析结果'}
            </h3>
            <div className="space-y-4">
              {formatAnalysisText(result.data?.analysis || result.message || '')}
            </div>
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="border-t border-gray-200 p-6 bg-white">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="text-sm text-gray-500 flex items-center">
              <span className="mr-2">⏰</span>
              {t('analysisCompleteTime') || '分析完成'}：{result.data?.timestamp ? new Date(result.data.timestamp).toLocaleString() : new Date().toLocaleString()}
            </div>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={copyToClipboard}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md ${
                  copySuccess
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-600 text-white hover:bg-gray-700'
                }`}
              >
                <Copy className="w-4 h-4" />
                <span>{copySuccess ? (t('copied') || '已复制!') : (t('copyResult') || '复制结果')}</span>
              </button>
              <button
                onClick={downloadResult}
                className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <Download className="w-4 h-4" />
                <span>{t('downloadFile') || '下载文件'}</span>
              </button>
              <button
                onClick={onClose}
                className="px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
              >
                {t('close') || '关闭'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FortuneResultModal;
