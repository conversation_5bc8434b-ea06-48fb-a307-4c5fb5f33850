const axios = require('axios');

async function testActualFortune() {
  console.log('🔮 测试实际算命API端点...\n');

  const baseURL = 'http://localhost:3001/api';
  let authToken = null;

  try {
    // 1. 登录获取token
    console.log('🔐 1. 用户登录...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.success) {
      authToken = loginResponse.data.data.token;
      console.log('✅ 登录成功');
    } else {
      throw new Error('登录失败');
    }

    // 2. 测试每日运势
    console.log('\n🌟 2. 测试每日运势...');
    try {
      const dailyResponse = await axios.post(`${baseURL}/fortune/daily`, {}, {
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'X-Language': 'zh-CN'
        }
      });

      if (dailyResponse.data.success) {
        console.log('✅ 每日运势API正常');
        const result = dailyResponse.data.data?.result || dailyResponse.data.data || '运势结果';
        console.log('🎯 运势结果:', typeof result === 'string' ? result.substring(0, 100) + '...' : JSON.stringify(result).substring(0, 100) + '...');
      } else {
        console.log('⚠️ 每日运势API错误:', dailyResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 每日运势API失败:', error.response?.status, error.response?.data?.message || error.message);
    }

    // 3. 测试八字精算
    console.log('\n🎯 3. 测试八字精算...');
    try {
      const baziResponse = await axios.post(`${baseURL}/fortune/bazi`, {}, {
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'X-Language': 'zh-CN'
        }
      });

      if (baziResponse.data.success) {
        console.log('✅ 八字精算API正常');
        console.log('🎯 八字结果:', baziResponse.data.data.result.substring(0, 100) + '...');
      } else {
        console.log('⚠️ 八字精算API错误:', baziResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 八字精算API失败:', error.response?.status, error.response?.data?.message || error.message);
    }

    // 4. 测试塔罗占卜
    console.log('\n🔮 4. 测试塔罗占卜...');
    try {
      const tarotResponse = await axios.post(`${baseURL}/fortune/tarot`, {
        question: '我今年的事业运势如何？'
      }, {
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'X-Language': 'zh-CN'
        }
      });

      if (tarotResponse.data.success) {
        console.log('✅ 塔罗占卜API正常');
        console.log('🎯 占卜结果:', tarotResponse.data.data.result.substring(0, 100) + '...');
      } else {
        console.log('⚠️ 塔罗占卜API错误:', tarotResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 塔罗占卜API失败:', error.response?.status, error.response?.data?.message || error.message);
    }

    // 5. 测试幸运物品推荐
    console.log('\n🍀 5. 测试幸运物品推荐...');
    try {
      const luckyResponse = await axios.post(`${baseURL}/fortune/lucky-items`, {}, {
        headers: { 
          'Authorization': `Bearer ${authToken}`,
          'X-Language': 'zh-CN'
        }
      });

      if (luckyResponse.data.success) {
        console.log('✅ 幸运物品API正常');
        console.log('🎯 推荐结果:', luckyResponse.data.data.result.substring(0, 100) + '...');
      } else {
        console.log('⚠️ 幸运物品API错误:', luckyResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 幸运物品API失败:', error.response?.status, error.response?.data?.message || error.message);
    }

    // 6. 测试历史记录
    console.log('\n📚 6. 测试历史记录...');
    try {
      const historyResponse = await axios.get(`${baseURL}/fortune/history`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      });

      if (historyResponse.data.success) {
        console.log('✅ 历史记录API正常');
        console.log(`📊 历史记录数量: ${historyResponse.data.data.length}`);
      } else {
        console.log('⚠️ 历史记录API错误:', historyResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 历史记录API失败:', error.response?.status, error.response?.data?.message || error.message);
    }

    console.log('\n🎉 算命功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testActualFortune();
