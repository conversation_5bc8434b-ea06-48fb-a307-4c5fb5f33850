const axios = require('axios');

async function testFrontendMembership() {
  console.log('🎨 测试前端会员状态API...\n');

  const baseURL = 'http://localhost:3001/api';
  let authToken = null;

  try {
    // 1. 登录获取token
    console.log('1. 用户登录...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.success) {
      authToken = loginResponse.data.data.token;
      console.log('✅ 登录成功');
    } else {
      throw new Error('登录失败');
    }

    const headers = { 'Authorization': `Bearer ${authToken}` };

    // 2. 测试会员状态API
    console.log('\n2. 测试会员状态API...');
    const membershipResponse = await axios.get(`${baseURL}/membership/status`, { headers });
    
    console.log('📋 完整响应数据:');
    console.log(JSON.stringify(membershipResponse.data, null, 2));

    if (membershipResponse.data.success) {
      const data = membershipResponse.data.data;
      console.log('\n✅ 会员状态解析:');
      console.log(`   计划ID: ${data.plan_id}`);
      console.log(`   是否激活: ${data.is_active}`);
      console.log(`   过期时间: ${data.expires_at}`);
      console.log(`   剩余积分: ${data.remaining_credits}`);
      
      if (data.plan) {
        console.log(`   计划名称: ${data.plan.name}`);
        console.log(`   计划等级: ${data.plan.level}`);
      }

      if (data.features) {
        console.log('   功能权限:');
        Object.entries(data.features).forEach(([feature, hasAccess]) => {
          console.log(`     ${feature}: ${hasAccess ? '✅' : '❌'}`);
        });
      }

      // 检查是否过期
      if (data.expires_at) {
        const expiresAt = new Date(data.expires_at);
        const now = new Date();
        const isExpired = now > expiresAt;
        console.log(`   是否过期: ${isExpired ? '是' : '否'}`);
        
        if (!isExpired) {
          const daysLeft = Math.ceil((expiresAt - now) / (1000 * 60 * 60 * 24));
          console.log(`   剩余天数: ${daysLeft} 天`);
        }
      }
    }

    // 3. 测试用户资料API中的会员信息
    console.log('\n3. 测试用户资料API中的会员信息...');
    const profileResponse = await axios.get(`${baseURL}/user/profile`, { headers });
    
    if (profileResponse.data.success && profileResponse.data.user.membership) {
      const membership = profileResponse.data.user.membership;
      console.log('✅ 用户资料中的会员信息:');
      console.log(`   计划ID: ${membership.plan_id}`);
      console.log(`   是否激活: ${membership.is_active}`);
      console.log(`   过期时间: ${membership.expires_at}`);
      console.log(`   剩余积分: ${membership.remaining_credits}`);
    }

    console.log('\n🎉 前端会员状态API测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误详情:', error.response.data);
    }
  }
}

testFrontendMembership();
