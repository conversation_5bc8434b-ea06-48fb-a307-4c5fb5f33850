# 📧 QQ邮箱SMTP配置指南

## 🎯 需要提供的信息

请提供以下信息来完成QQ邮箱配置：

### 1. QQ邮箱地址
- 格式：`<EMAIL>`
- 例如：`<EMAIL>`

### 2. QQ邮箱授权码
- **注意：不是QQ密码！**
- 16位字符的授权码
- 例如：`abcdefghijklmnop`

## 🔧 获取QQ邮箱授权码步骤

### 步骤1：登录QQ邮箱
1. 访问：https://mail.qq.com
2. 使用QQ号和密码登录

### 步骤2：进入设置
1. 点击右上角"设置"
2. 选择"账户"选项卡

### 步骤3：开启SMTP服务
1. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
2. 开启以下服务之一：
   - ✅ "IMAP/SMTP服务"（推荐）
   - ✅ "POP3/SMTP服务"

### 步骤4：生成授权码
1. 点击"生成授权码"
2. 按提示发送短信验证
3. 记录生成的16位授权码
4. **重要：妥善保存此授权码！**

## ⚙️ 配置文件

获得信息后，请更新 `backend/.env` 文件：

```env
# 邮件服务配置 - QQ邮箱SMTP
EMAIL_SERVICE=qq
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=你的QQ邮箱@qq.com
EMAIL_PASS=你的16位授权码
```

## 🧪 测试配置

配置完成后，运行测试脚本：

```bash
cd backend
node scripts/testEmail.js
```

## 📋 QQ邮箱SMTP参数

| 参数 | 值 |
|------|-----|
| SMTP服务器 | smtp.qq.com |
| 端口 | 587 (推荐) 或 465 |
| 加密方式 | STARTTLS |
| 用户名 | 完整QQ邮箱地址 |
| 密码 | 授权码（非QQ密码） |

## ❓ 常见问题

### Q: 为什么不能使用QQ密码？
A: 为了安全，QQ邮箱要求使用专门的授权码，而不是QQ登录密码。

### Q: 授权码在哪里查看？
A: 授权码只在生成时显示一次，请务必保存。如果丢失，需要重新生成。

### Q: 可以生成多个授权码吗？
A: 可以，每个授权码对应一个第三方应用。

### Q: 邮件发送失败怎么办？
A: 
1. 检查授权码是否正确
2. 确认SMTP服务已开启
3. 检查网络连接
4. 运行测试脚本查看详细错误

## 🔒 安全提示

1. **授权码保密**：授权码等同于密码，请勿泄露
2. **定期更换**：建议定期更换授权码
3. **删除不用的授权码**：及时删除不再使用的授权码
4. **监控异常**：注意邮箱异常登录提醒

## 📞 需要帮助？

如果遇到问题，请提供：
1. 错误信息截图
2. 测试脚本输出
3. 配置的邮箱地址（隐藏敏感信息）

---

配置完成后，系统将能够发送真实的验证码邮件！🎉
