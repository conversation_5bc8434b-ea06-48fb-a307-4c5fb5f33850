const path = require('path');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 导入后端的数据库配置
const { dbGet, dbAll } = require('./backend/config/database');

async function checkMembership() {
  console.log('🔍 检查测试用户会员状态...\n');

  try {
    // 查询测试用户信息
    const user = await dbGet(
      'SELECT id, email, name FROM users WHERE email = ?',
      ['<EMAIL>']
    );

    if (!user) {
      console.log('❌ 测试用户不存在');
      return;
    }

    console.log('👤 用户信息:');
    console.log(`   ID: ${user.id}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   姓名: ${user.name}`);

    // 查询会员信息
    const memberships = await dbAll(
      'SELECT * FROM memberships WHERE user_id = ? ORDER BY created_at DESC',
      [user.id]
    );

    console.log('\n💎 会员记录:');
    if (memberships.length === 0) {
      console.log('   无会员记录');
    } else {
      memberships.forEach((membership, index) => {
        console.log(`   记录 ${index + 1}:`);
        console.log(`     计划: ${membership.plan_id}`);
        console.log(`     状态: ${membership.is_active ? '激活' : '未激活'}`);
        console.log(`     剩余积分: ${membership.remaining_credits}`);
        console.log(`     过期时间: ${membership.expires_at}`);
        console.log(`     创建时间: ${membership.created_at}`);
        console.log(`     更新时间: ${membership.updated_at}`);
        
        // 检查是否过期
        const expiresAt = new Date(membership.expires_at);
        const now = new Date();
        const isExpired = expiresAt < now;
        console.log(`     是否过期: ${isExpired ? '是' : '否'}`);
        console.log('');
      });
    }

    // 查询会员计划
    const plans = await dbAll('SELECT * FROM membership_plans');

    console.log('📋 可用会员计划:');
    plans.forEach(plan => {
      console.log(`   ${plan.id}: ${plan.name} - ${plan.price}元`);
      console.log(`     描述: ${plan.description}`);
      console.log(`     积分: ${plan.credits}`);
      console.log(`     有效期: ${plan.duration_days}天`);
      console.log('');
    });

    // 检查当前激活的会员
    const activeMembership = await dbGet(`
      SELECT * FROM memberships 
      WHERE user_id = ? AND is_active = TRUE AND expires_at > datetime('now')
      ORDER BY created_at DESC LIMIT 1
    `, [user.id]);

    console.log('🎯 当前激活会员:');
    if (activeMembership) {
      console.log(`   计划: ${activeMembership.plan_id}`);
      console.log(`   剩余积分: ${activeMembership.remaining_credits}`);
      console.log(`   过期时间: ${activeMembership.expires_at}`);
    } else {
      console.log('   无激活会员');
    }

  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  }
}

checkMembership();
