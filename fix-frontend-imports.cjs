const fs = require('fs');
const path = require('path');

console.log('🔧 修复前端导入问题...\n');

// 需要检查的文件
const problematicFiles = [
  'src/lib/config.ts',
  'src/lib/email-service.ts',
  'src/lib/db.ts',
  'src/lib/api-handler.ts',
  'src/lib/permission-service.ts',
  'src/lib/push-notification-service.ts',
  'src/lib/scheduler-service.ts',
  'src/lib/stripe-service.ts'
];

// 后端专用的导入模式
const backendImports = [
  'nodemailer',
  'prisma',
  'sqlite3',
  'bcryptjs',
  'jsonwebtoken',
  'express',
  'cors',
  'helmet',
  'joi',
  'rate-limiter-flexible',
  'dotenv'
];

function fixFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ 文件不存在: ${filePath}`);
    return;
  }

  console.log(`🔍 检查文件: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let issues = [];

  // 检查每个后端导入
  backendImports.forEach(importName => {
    const patterns = [
      new RegExp(`import.*from\\s+['"]${importName}['"]`, 'g'),
      new RegExp(`import\\s+${importName}\\s+from`, 'g'),
      new RegExp(`require\\(['"]${importName}['"]\\)`, 'g')
    ];

    patterns.forEach(pattern => {
      if (pattern.test(content)) {
        issues.push(`发现后端导入: ${importName}`);
      }
    });
  });

  // 检查 prisma 导入
  if (content.includes('import prisma') || content.includes('from \'./db\'')) {
    issues.push('发现数据库导入');
  }

  if (issues.length > 0) {
    console.log(`❌ ${filePath} 有问题:`);
    issues.forEach(issue => console.log(`   - ${issue}`));
    
    // 创建备份
    const backupPath = filePath + '.backup';
    fs.writeFileSync(backupPath, content);
    console.log(`📁 已创建备份: ${backupPath}`);
    
    // 注释掉有问题的导入
    let fixedContent = content;
    
    // 注释掉后端导入
    backendImports.forEach(importName => {
      const patterns = [
        new RegExp(`^(import.*from\\s+['"]${importName}['"];?)$`, 'gm'),
        new RegExp(`^(import\\s+${importName}\\s+from.*;?)$`, 'gm')
      ];

      patterns.forEach(pattern => {
        fixedContent = fixedContent.replace(pattern, '// $1 // 后端专用，已注释');
      });
    });

    // 注释掉 prisma 相关导入
    fixedContent = fixedContent.replace(
      /^(import.*from\s+['"]\.\/db['"];?)$/gm, 
      '// $1 // 数据库导入，已注释'
    );

    // 写入修复后的文件
    fs.writeFileSync(filePath, fixedContent);
    console.log(`✅ 已修复: ${filePath}`);
    modified = true;
  } else {
    console.log(`✅ ${filePath} 没有问题`);
  }

  return modified;
}

// 修复所有问题文件
let totalFixed = 0;
problematicFiles.forEach(file => {
  if (fixFile(file)) {
    totalFixed++;
  }
});

console.log(`\n🎉 修复完成！`);
console.log(`📊 总共修复了 ${totalFixed} 个文件`);
console.log(`💡 备份文件已创建，如需恢复可使用 .backup 文件`);

if (totalFixed > 0) {
  console.log(`\n🚀 现在可以尝试重新启动前端服务:`);
  console.log(`   npm run dev`);
}
