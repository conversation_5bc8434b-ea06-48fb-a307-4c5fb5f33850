const axios = require('axios');

async function testConnection() {
  console.log('🔗 测试前端后端连接...\n');

  try {
    // 1. 测试前端是否运行
    console.log('🌐 测试前端服务 (端口5173)...');
    try {
      const frontendResponse = await axios.get('http://localhost:5173', {
        timeout: 5000,
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
      });
      console.log('✅ 前端服务正常运行');
    } catch (frontendError) {
      console.log('⚠️ 前端服务连接问题:', frontendError.message);
    }

    // 2. 测试后端是否运行
    console.log('\n🔧 测试后端服务 (端口3001)...');
    const backendResponse = await axios.get('http://localhost:3001/api/health');
    
    if (backendResponse.status === 200) {
      console.log('✅ 后端服务正常运行');
      console.log('📊 状态:', backendResponse.data.status);
      console.log('📝 消息:', backendResponse.data.message);
    }

    // 3. 测试跨域设置
    console.log('\n🌍 测试CORS设置...');
    try {
      const corsResponse = await axios.get('http://localhost:3001/api/health', {
        headers: {
          'Origin': 'http://localhost:5173',
          'Access-Control-Request-Method': 'GET'
        }
      });
      console.log('✅ CORS设置正常');
    } catch (corsError) {
      console.log('⚠️ CORS可能有问题:', corsError.message);
    }

    // 4. 测试登录API
    console.log('\n🔐 测试登录API...');
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', loginData, {
      headers: {
        'Origin': 'http://localhost:5173',
        'Content-Type': 'application/json'
      }
    });
    
    if (loginResponse.data.success) {
      console.log('✅ 登录API正常工作');
      console.log('👤 用户:', loginResponse.data.data.user.name);
      console.log('📧 邮箱:', loginResponse.data.data.user.email);
    }

    console.log('\n🎉 前端后端连接测试完成！');
    console.log('💡 现在可以在浏览器中访问: http://localhost:5173');
    console.log('💡 使用测试账号登录: <EMAIL> / password123');

  } catch (error) {
    console.error('❌ 连接测试失败:', error.response?.data?.message || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 解决方案:');
      console.log('1. 确保后端服务运行: cd backend && node server.js');
      console.log('2. 确保前端服务运行: npm run dev');
      console.log('3. 检查端口是否被占用');
    }
  }
}

testConnection();
