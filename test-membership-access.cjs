const axios = require('axios');

async function testMembershipAccess() {
  console.log('🔐 测试会员权限访问...\n');

  const baseURL = 'http://localhost:3001/api';
  let authToken = null;

  try {
    // 1. 登录获取token
    console.log('1. 用户登录...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (loginResponse.data.success) {
      authToken = loginResponse.data.data.token;
      console.log('✅ 登录成功');
    } else {
      throw new Error('登录失败');
    }

    // 2. 检查会员状态
    console.log('\n2. 检查会员状态...');
    const membershipResponse = await axios.get(`${baseURL}/membership/status`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    if (membershipResponse.data.success) {
      console.log('✅ 会员状态获取成功');
      const membership = membershipResponse.data.data;
      if (membership && membership.plan_id !== 'free') {
        console.log(`   计划: ${membership.plan_id}`);
        console.log(`   状态: ${membership.is_active ? '激活' : '未激活'}`);
        console.log(`   剩余积分: ${membership.remaining_credits}`);
        console.log(`   过期时间: ${membership.expires_at}`);
        if (membership.plan) {
          console.log(`   计划名称: ${membership.plan.name}`);
        }
      } else {
        console.log('   当前为免费计划');
      }
    } else {
      console.log('⚠️ 会员状态获取失败:', membershipResponse.data.message);
    }

    // 3. 测试服务访问权限
    console.log('\n3. 测试服务访问权限...');
    const accessResponse = await axios.post(`${baseURL}/membership/check-access`, {
      serviceId: 'fortune-telling'
    }, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    if (accessResponse.data.success) {
      console.log('✅ 服务访问权限检查成功');
      console.log(`   允许访问: ${accessResponse.data.hasAccess ? '是' : '否'}`);
      if (accessResponse.data.message) {
        console.log(`   消息: ${accessResponse.data.message}`);
      }
    } else {
      console.log('⚠️ 服务访问权限检查失败:', accessResponse.data.message);
    }

    // 4. 测试算命API访问
    console.log('\n4. 测试算命API访问...');
    try {
      const fortuneResponse = await axios.post(`${baseURL}/fortune/bazi`, {
        birthYear: 1990,
        birthMonth: 5,
        birthDay: 15,
        birthHour: 14,
        gender: 'male',
        birthPlace: '北京市'
      }, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      });

      if (fortuneResponse.data.success) {
        console.log('✅ 算命API访问成功');
        console.log(`   结果长度: ${fortuneResponse.data.result?.length || 0} 字符`);
      } else {
        console.log('⚠️ 算命API访问失败:', fortuneResponse.data.message);
      }
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('❌ 算命API访问被拒绝 - 权限不足');
        console.log(`   错误: ${error.response.data.message}`);
      } else {
        console.log('❌ 算命API访问错误:', error.message);
      }
    }

    console.log('\n🎉 会员权限测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误详情:', error.response.data);
    }
  }
}

testMembershipAccess();
